const { chromium } = require('playwright');
const { createClient } = require('@supabase/supabase-js');
const cron = require('node-cron');

// ==================== 配置 ====================
const CONFIG = {
  // Supabase 配置
  SUPABASE_URL: 'https://wjanjmsywbydjbfrdkaz.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE',
  
  // 小红书配置
  XHS_URL: 'https://www.xiaohongshu.com/explore?channel_id=homefeed_recommend',
  
  // 获取配置
  COOKIES_PER_RUN: 5,        // 每次运行获取的cookie数量
  DELAY_BETWEEN: 5000,       // 每个cookie获取之间的延迟(毫秒)
  PAGE_WAIT_TIME: 8000,      // 页面加载等待时间(毫秒)
  
  // 定时任务配置
  CRON_SCHEDULE: '0 * * * *', // 每小时整点运行
  RUN_IMMEDIATELY: true,       // 启动时立即运行一次
  
  // 重试配置
  MAX_RETRIES: 3,            // 最大重试次数
  RETRY_DELAY: 10000,        // 重试延迟(毫秒)
};

// ==================== 初始化 ====================
const supabase = createClient(CONFIG.SUPABASE_URL, CONFIG.SUPABASE_KEY);

// ==================== 工具函数 ====================
/**
 * 获取当前时间字符串
 */
function getCurrentTime() {
  return new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
}

/**
 * 日志输出
 */
function log(level, message, data = null) {
  const timestamp = getCurrentTime();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  
  if (level === 'ERROR') {
    console.error(logMessage, data || '');
  } else {
    console.log(logMessage, data ? JSON.stringify(data) : '');
  }
}

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ==================== 核心功能 ====================
/**
 * 获取单个小红书游客cookie
 */
async function getSingleXhsCookie(attemptNumber = 1) {
  let browser = null;
  
  try {
    log('INFO', `开始获取第 ${attemptNumber} 个游客cookie`);
    
    // 启动浏览器（无头模式）
    browser = await chromium.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-blink-features=AutomationControlled'
      ]
    });
    
    // 创建隐身上下文
    const context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1920, height: 1080 },
      locale: 'zh-CN',
      timezoneId: 'Asia/Shanghai',
      // 设置额外的请求头
      extraHTTPHeaders: {
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
      }
    });
    
    const page = await context.newPage();
    
    // 访问小红书
    log('INFO', `访问: ${CONFIG.XHS_URL}`);
    await page.goto(CONFIG.XHS_URL, {
      waitUntil: 'domcontentloaded',  // 改为更宽松的加载策略
      timeout: 60000  // 增加超时时间
    });
    
    // 等待页面加载
    await delay(CONFIG.PAGE_WAIT_TIME);
    
    // 获取cookies
    const cookies = await context.cookies();
    const xhsCookies = cookies.filter(cookie => 
      cookie.domain.includes('xiaohongshu.com')
    );
    
    if (xhsCookies.length === 0) {
      throw new Error('未获取到有效的小红书cookies');
    }
    
    // 转换为cookie字符串
    const cookieString = xhsCookies
      .map(cookie => `${cookie.name}=${cookie.value}`)
      .join('; ');
    
    log('INFO', `成功获取cookie，包含 ${xhsCookies.length} 个字段`);
    
    return {
      cookieString,
      cookieCount: xhsCookies.length,
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    log('ERROR', `获取cookie失败: ${error.message}`);
    throw error;
    
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

/**
 * 批量获取cookies并存储到数据库
 */
async function fetchAndStoreCookies() {
  log('INFO', '========== 开始批量获取小红书游客cookies ==========');
  
  const results = {
    success: 0,
    failed: 0,
    errors: []
  };
  
  for (let i = 1; i <= CONFIG.COOKIES_PER_RUN; i++) {
    let retries = 0;
    let success = false;
    
    while (retries < CONFIG.MAX_RETRIES && !success) {
      try {
        // 获取cookie
        const cookieData = await getSingleXhsCookie(i);
        
        // 存储到数据库
        const { error } = await supabase
          .from('cookie')
          .insert([{
            cookie: cookieData.cookieString,
            platform: 'red',
            guest: true,
            wasted: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }]);
        
        if (error) {
          throw error;
        }
        
        log('INFO', `✅ 第 ${i}/${CONFIG.COOKIES_PER_RUN} 个cookie存储成功`);
        results.success++;
        success = true;
        
        // 如果不是最后一个，延迟后继续
        if (i < CONFIG.COOKIES_PER_RUN) {
          await delay(CONFIG.DELAY_BETWEEN);
        }
        
      } catch (error) {
        retries++;
        log('ERROR', `第 ${i} 个cookie处理失败 (尝试 ${retries}/${CONFIG.MAX_RETRIES})`, error.message);
        
        if (retries >= CONFIG.MAX_RETRIES) {
          results.failed++;
          results.errors.push({
            index: i,
            error: error.message
          });
        } else {
          await delay(CONFIG.RETRY_DELAY);
        }
      }
    }
  }
  
  // 输出统计结果
  log('INFO', '========== 批量获取完成 ==========');
  log('INFO', `成功: ${results.success}/${CONFIG.COOKIES_PER_RUN}`);
  if (results.failed > 0) {
    log('WARN', `失败: ${results.failed}/${CONFIG.COOKIES_PER_RUN}`);
    results.errors.forEach(err => {
      log('ERROR', `  - 第 ${err.index} 个: ${err.error}`);
    });
  }
  
  // 查询当前数据库中的cookie总数
  await showDatabaseStats();
  
  return results;
}

/**
 * 显示数据库统计信息
 */
async function showDatabaseStats() {
  try {
    // 查询总数
    const { count: totalCount } = await supabase
      .from('cookie')
      .select('*', { count: 'exact', head: true })
      .eq('platform', 'red')
      .eq('guest', true);
    
    // 查询未使用的数量
    const { count: unusedCount } = await supabase
      .from('cookie')
      .select('*', { count: 'exact', head: true })
      .eq('platform', 'red')
      .eq('guest', true)
      .eq('wasted', false);
    
    log('INFO', `数据库统计: 总计 ${totalCount} 个小红书游客cookie，其中 ${unusedCount} 个未使用`);
    
  } catch (error) {
    log('ERROR', '获取数据库统计失败', error.message);
  }
}

/**
 * 清理过期或已使用的cookies（可选功能）
 */
async function cleanupOldCookies() {
  try {
    // 删除7天前创建且已使用的cookies
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const { data, error } = await supabase
      .from('cookie')
      .delete()
      .eq('platform', 'red')
      .eq('guest', true)
      .eq('wasted', true)
      .lt('created_at', sevenDaysAgo.toISOString())
      .select();
    
    if (!error && data && data.length > 0) {
      log('INFO', `清理了 ${data.length} 个过期的已使用cookie`);
    }
    
  } catch (error) {
    log('ERROR', '清理过期cookies失败', error.message);
  }
}

// ==================== 主程序 ====================
/**
 * 执行任务
 */
async function runTask() {
  try {
    log('INFO', '开始执行定时任务');
    
    // 清理过期cookies（可选）
    await cleanupOldCookies();
    
    // 获取并存储新cookies
    await fetchAndStoreCookies();
    
    log('INFO', '定时任务执行完成');
    
  } catch (error) {
    log('ERROR', '任务执行失败', error);
  }
}

/**
 * 启动定时任务
 */
function startScheduler() {
  log('INFO', '========== Cookie自动获取程序启动 ==========');
  log('INFO', `配置: 每小时获取 ${CONFIG.COOKIES_PER_RUN} 个小红书游客cookie`);
  log('INFO', `定时规则: ${CONFIG.CRON_SCHEDULE}`);
  
  // 如果配置立即运行，先执行一次
  if (CONFIG.RUN_IMMEDIATELY) {
    log('INFO', '立即执行第一次任务...');
    runTask();
  }
  
  // 设置定时任务
  cron.schedule(CONFIG.CRON_SCHEDULE, () => {
    runTask();
  });
  
  log('INFO', '定时任务已启动，等待下次执行...');
  
  // 优雅退出处理
  process.on('SIGINT', () => {
    log('INFO', '收到退出信号，程序即将关闭...');
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    log('INFO', '收到终止信号，程序即将关闭...');
    process.exit(0);
  });
}

// ==================== 命令行入口 ====================
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'run':
      // 立即运行一次
      await runTask();
      break;
      
    case 'stats':
      // 显示统计信息
      await showDatabaseStats();
      break;
      
    case 'clean':
      // 清理过期cookies
      await cleanupOldCookies();
      break;
      
    case 'start':
    default:
      // 启动定时任务
      startScheduler();
      break;
  }
}

// 程序入口
if (require.main === module) {
  main().catch(error => {
    log('ERROR', '程序异常退出', error);
    process.exit(1);
  });
}

module.exports = {
  fetchAndStoreCookies,
  showDatabaseStats,
  cleanupOldCookies,
  runTask
};