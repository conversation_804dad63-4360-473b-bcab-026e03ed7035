{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(npm install:*)", "<PERSON><PERSON>(npx playwright:*)", "<PERSON><PERSON>(sudo npx playwright:*)", "Bash(kill:*)", "Bash(node:*)", "Bash(rm:*)", "mcp__supabase__list_projects", "mcp__supabase__execute_sql", "mcp__supabase__list_tables", "Bash(SUPABASE_KEY='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE' node store_xhs_cookies_to_db.js file)", "Bash(SUPABASE_KEY='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE' node store_xhs_cookies_to_db.js query)"], "deny": [], "ask": []}, "enableAllProjectMcpServers": false}