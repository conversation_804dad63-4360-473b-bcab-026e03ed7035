{"dependencies": {"@supabase/node-fetch": "2.6.13", "@supabase/supabase-js": "2.45.0", "async.queue": "0.5.2", "axios": "^1.9.0", "bull": "4.16.3", "check-disk-space": "3.4.0", "cluster-key-slot": "1.1.2", "disk-space": "1.1.0", "dotenv": "^16.5.0", "fluent-ffmpeg": "2.1.3", "ioredis": "5.4.1", "js-yaml": "^4.1.0", "node-cron": "3.0.3", "playwright": "^1.55.0", "ps-node": "^0.1.6", "redis": "4.7.0", "redis-errors": "1.2.0", "telegraf": "^4.16.3", "tr46": "5.0.0", "webidl-conversions": "7.0.0", "whatwg-url": "14.0.0", "winston": "3.14.2"}, "name": "downloader", "version": "1.0.0", "main": "listen.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC"}