const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');

// 配置
const XHS_URL = 'https://www.xiaohongshu.com/explore?channel_id=homefeed_recommend';
const WAIT_TIME = 5000; // 等待页面加载的时间（毫秒）
const COOKIE_FILE = path.join(__dirname, 'xhs_guest_cookies.json');

/**
 * 获取单个游客 cookie
 * @returns {Promise<Object>} 包含 cookies 和时间戳的对象
 */
async function getGuestCookie() {
  console.log('启动浏览器...');
  
  // 使用隐身模式（无痕浏览）
  const browser = await chromium.launch({
    headless: true, // 无头模式
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu'
    ]
  });

  try {
    // 创建新的隐身浏览器上下文
    const context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1920, height: 1080 },
      locale: 'zh-CN',
      timezoneId: 'Asia/Shanghai'
    });

    // 创建新页面
    const page = await context.newPage();
    
    console.log(`访问小红书: ${XHS_URL}`);
    
    // 访问小红书主页
    await page.goto(XHS_URL, {
      waitUntil: 'networkidle',
      timeout: 30000
    });
    
    // 等待页面完全加载
    console.log(`等待 ${WAIT_TIME / 1000} 秒以确保页面完全加载...`);
    await page.waitForTimeout(WAIT_TIME);
    
    // 获取所有 cookies
    const cookies = await context.cookies();
    
    // 筛选重要的 cookies
    const importantCookies = cookies.filter(cookie => {
      // 保留所有小红书域名的 cookies
      return cookie.domain.includes('xiaohongshu.com');
    });
    
    console.log(`获取到 ${importantCookies.length} 个 cookies`);
    
    // 将 cookies 转换为字符串格式（用于请求头）
    const cookieString = importantCookies
      .map(cookie => `${cookie.name}=${cookie.value}`)
      .join('; ');
    
    return {
      cookies: importantCookies,
      cookieString: cookieString,
      timestamp: new Date().toISOString(),
      url: XHS_URL
    };
    
  } finally {
    await browser.close();
    console.log('浏览器已关闭');
  }
}

/**
 * 获取多个游客 cookies
 * @param {number} count 需要获取的数量
 * @param {number} delay 每次获取之间的延迟（毫秒）
 * @returns {Promise<Array>} cookies 数组
 */
async function getMultipleGuestCookies(count = 5, delay = 3000) {
  const allCookies = [];
  
  for (let i = 1; i <= count; i++) {
    console.log(`\n========== 获取第 ${i}/${count} 个游客 Cookie ==========`);
    
    try {
      const cookieData = await getGuestCookie();
      allCookies.push({
        ...cookieData,
        id: i,
        name: `guest_${i}`
      });
      
      console.log(`✅ 成功获取第 ${i} 个 cookie`);
      
      // 打印部分 cookie 信息
      const sampleCookies = cookieData.cookies.slice(0, 3);
      console.log('示例 cookies:');
      sampleCookies.forEach(cookie => {
        console.log(`  - ${cookie.name}: ${cookie.value.substring(0, 20)}...`);
      });
      
      // 如果不是最后一个，等待一段时间
      if (i < count) {
        console.log(`等待 ${delay / 1000} 秒后继续...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
    } catch (error) {
      console.error(`❌ 获取第 ${i} 个 cookie 失败:`, error.message);
    }
  }
  
  return allCookies;
}

/**
 * 保存 cookies 到文件
 * @param {Array} cookies cookies 数组
 */
async function saveCookiesToFile(cookies) {
  try {
    await fs.writeFile(COOKIE_FILE, JSON.stringify(cookies, null, 2));
    console.log(`\n✅ Cookies 已保存到: ${COOKIE_FILE}`);
  } catch (error) {
    console.error('❌ 保存 cookies 失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('开始获取小红书游客 Cookies...\n');
  
  // 获取命令行参数
  const args = process.argv.slice(2);
  const count = parseInt(args[0]) || 3; // 默认获取3个
  const delay = parseInt(args[1]) || 3000; // 默认延迟3秒
  
  console.log(`配置: 获取 ${count} 个 cookies，每次间隔 ${delay / 1000} 秒\n`);
  
  // 获取多个游客 cookies
  const cookies = await getMultipleGuestCookies(count, delay);
  
  // 显示结果统计
  console.log('\n========== 获取完成 ==========');
  console.log(`成功获取 ${cookies.length} 个游客 cookies`);
  
  // 保存到文件
  if (cookies.length > 0) {
    await saveCookiesToFile(cookies);
    
    // 显示使用示例
    console.log('\n使用示例:');
    console.log('const cookiesData = require("./xhs_guest_cookies.json");');
    console.log('const cookieString = cookiesData[0].cookieString; // 获取第一个 cookie 字符串');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

// 导出函数供其他模块使用
module.exports = {
  getGuestCookie,
  getMultipleGuestCookies,
  saveCookiesToFile
};