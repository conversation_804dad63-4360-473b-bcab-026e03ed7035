const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');
const { getMultipleGuestCookies } = require('./get_xhs_cookies');

// Supabase 配置
const SUPABASE_URL = 'https://wjanjmsywbydjbfrdkaz.supabase.co';
const SUPABASE_KEY = process.env.SUPABASE_KEY || ''; // 需要设置环境变量

// 创建 Supabase 客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * 从文件读取已获取的 cookies
 * @param {string} filePath cookie 文件路径
 * @returns {Promise<Array>} cookies 数组
 */
async function readCookiesFromFile(filePath = './xhs_guest_cookies.json') {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('读取 cookie 文件失败:', error.message);
    return [];
  }
}

/**
 * 将单个 cookie 存储到数据库
 * @param {Object} cookieData cookie 数据对象
 * @returns {Promise<Object>} 存储结果
 */
async function storeCookieToDatabase(cookieData) {
  try {
    // 准备数据
    const dbData = {
      cookie: cookieData.cookieString,
      platform: 'red',
      guest: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    console.log(`存储 cookie 到数据库...`);
    
    // 插入到数据库
    const { data, error } = await supabase
      .from('cookie')
      .insert([dbData])
      .select();
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Cookie 成功存储到数据库');
    return { success: true, data };
    
  } catch (error) {
    console.error('❌ 存储失败:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 批量存储 cookies 到数据库
 * @param {Array} cookies cookies 数组
 * @returns {Promise<Object>} 批量存储结果
 */
async function batchStoreCookies(cookies) {
  if (!cookies || cookies.length === 0) {
    console.log('没有 cookies 需要存储');
    return { success: false, message: '没有 cookies' };
  }
  
  console.log(`准备存储 ${cookies.length} 个 cookies 到数据库...\n`);
  
  const results = {
    success: [],
    failed: [],
    total: cookies.length
  };
  
  // 批量准备数据
  const dbDataArray = cookies.map(cookieData => ({
    cookie: cookieData.cookieString,
    platform: 'red',
    guest: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }));
  
  try {
    // 批量插入
    const { data, error } = await supabase
      .from('cookie')
      .insert(dbDataArray)
      .select();
    
    if (error) {
      throw error;
    }
    
    results.success = data || [];
    console.log(`✅ 成功存储 ${results.success.length} 个 cookies`);
    
  } catch (error) {
    console.error('❌ 批量存储失败:', error.message);
    
    // 如果批量失败，尝试逐个存储
    console.log('尝试逐个存储...');
    for (let i = 0; i < cookies.length; i++) {
      const result = await storeCookieToDatabase(cookies[i]);
      if (result.success) {
        results.success.push(result.data);
      } else {
        results.failed.push({ index: i, error: result.error });
      }
    }
  }
  
  return results;
}

/**
 * 获取并存储新的游客 cookies
 * @param {number} count 需要获取的数量
 * @param {number} delay 每次获取之间的延迟（毫秒）
 */
async function fetchAndStoreCookies(count = 3, delay = 3000) {
  console.log('开始获取并存储小红书游客 Cookies...\n');
  
  // 获取新的 cookies
  const cookies = await getMultipleGuestCookies(count, delay);
  
  if (cookies.length === 0) {
    console.log('没有获取到任何 cookies');
    return;
  }
  
  // 存储到数据库
  const results = await batchStoreCookies(cookies);
  
  // 显示结果
  console.log('\n========== 存储完成 ==========');
  console.log(`成功: ${results.success.length}/${results.total}`);
  if (results.failed.length > 0) {
    console.log(`失败: ${results.failed.length}`);
    results.failed.forEach(item => {
      console.log(`  - 索引 ${item.index}: ${item.error}`);
    });
  }
  
  // 保存到本地文件作为备份
  if (cookies.length > 0) {
    const backupFile = `xhs_cookies_backup_${Date.now()}.json`;
    await fs.writeFile(backupFile, JSON.stringify(cookies, null, 2));
    console.log(`\n备份已保存到: ${backupFile}`);
  }
}

/**
 * 从文件存储到数据库
 * @param {string} filePath cookie 文件路径
 */
async function storeFromFile(filePath = './xhs_guest_cookies.json') {
  console.log(`从文件 ${filePath} 读取并存储 cookies...\n`);
  
  const cookies = await readCookiesFromFile(filePath);
  
  if (cookies.length === 0) {
    console.log('文件中没有 cookies');
    return;
  }
  
  const results = await batchStoreCookies(cookies);
  
  // 显示结果
  console.log('\n========== 存储完成 ==========');
  console.log(`成功: ${results.success.length}/${results.total}`);
  if (results.failed.length > 0) {
    console.log(`失败: ${results.failed.length}`);
  }
}

/**
 * 查询数据库中的小红书游客 cookies
 */
async function queryGuestCookies() {
  try {
    const { data, error } = await supabase
      .from('cookie')
      .select('*')
      .eq('platform', 'red')
      .eq('guest', true)
      .order('created_at', { ascending: false });
    
    if (error) {
      throw error;
    }
    
    console.log(`\n数据库中有 ${data.length} 个小红书游客 cookies:`);
    data.forEach((item, index) => {
      console.log(`\n[${index + 1}] ID: ${item.id}`);
      console.log(`  创建时间: ${item.created_at}`);
      console.log(`  Cookie 示例: ${item.cookie.substring(0, 50)}...`);
    });
    
    return data;
    
  } catch (error) {
    console.error('查询失败:', error.message);
    return [];
  }
}

/**
 * 主函数
 */
async function main() {
  // 检查 Supabase Key
  if (!SUPABASE_KEY) {
    console.error('❌ 错误: 请设置环境变量 SUPABASE_KEY');
    console.log('使用方法: SUPABASE_KEY=your_key_here node store_xhs_cookies_to_db.js');
    process.exit(1);
  }
  
  // 获取命令行参数
  const args = process.argv.slice(2);
  const command = args[0] || 'file'; // 默认从文件存储
  
  switch (command) {
    case 'fetch':
      // 获取新的 cookies 并存储
      const count = parseInt(args[1]) || 3;
      const delay = parseInt(args[2]) || 3000;
      await fetchAndStoreCookies(count, delay);
      break;
      
    case 'file':
      // 从文件存储
      const filePath = args[1] || './xhs_guest_cookies.json';
      await storeFromFile(filePath);
      break;
      
    case 'query':
      // 查询数据库中的 cookies
      await queryGuestCookies();
      break;
      
    default:
      console.log('使用方法:');
      console.log('  SUPABASE_KEY=xxx node store_xhs_cookies_to_db.js fetch [count] [delay]  # 获取新 cookies 并存储');
      console.log('  SUPABASE_KEY=xxx node store_xhs_cookies_to_db.js file [filepath]        # 从文件存储');
      console.log('  SUPABASE_KEY=xxx node store_xhs_cookies_to_db.js query                  # 查询数据库中的 cookies');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

// 导出函数供其他模块使用
module.exports = {
  storeCookieToDatabase,
  batchStoreCookies,
  fetchAndStoreCookies,
  storeFromFile,
  queryGuestCookies
};